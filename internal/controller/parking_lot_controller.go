package controller

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type ParkingLotController struct {
	parkingLotUsecase       usecase.ParkingLotUsecase
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase
	logger                  *logger.Logger
}

func NewParkingLotController(
	parkingLotUsecase usecase.ParkingLotUsecase,
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase,
	logger *logger.Logger,
) *ParkingLotController {
	return &ParkingLotController{
		parkingLotUsecase:            parkingLotUsecase,
		adminParkingLotConfigUsecase: adminParkingLotConfigUsecase,
		logger:                       logger,
	}
}

func (plc *ParkingLotController) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lots", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots",
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lots feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot by ID", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot by ID feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot availability", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}/availability",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot availability feature not implemented yet")
}

// Admin Pricing Config Management
func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot pricing configs", map[string]interface{}{
		"method": "GET",
		"path":   "/admin/parking-lots/{lotId}/pricing-configs",
		"lot_id": lotId,
	})

	includeInactive := false
	if params.IncludeInactive != nil {
		includeInactive = *params.IncludeInactive
	}

	configs, err := plc.adminParkingLotConfigUsecase.GetByParkingLotID(c.Request.Context(), uuid.UUID(lotId), includeInactive)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to get parking lot configs")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to get parking lot configs")
		return
	}

	apiConfigs := make([]api.ParkingLotConfig, len(configs))
	for i, config := range configs {
		apiConfigs[i] = convertParkingLotConfigToAPI(config)
	}

	response.Success(c, apiConfigs)
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Creating parking lot pricing config", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/parking-lots/{lotId}/pricing-configs",
		"lot_id": lotId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to get user ID from context")
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid user authentication")
		return
	}

	var req api.CreatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	if req.ConfigName == "" {
		response.BadRequest(c, "VALIDATION_ERROR", "Config name is required", nil)
		return
	}

	pricingRules := convertAPIPricingRulesToDomain(req.PricingRules)

	config, err := plc.adminParkingLotConfigUsecase.Create(c.Request.Context(), uuid.UUID(lotId), req.ConfigName, pricingRules, userID)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to create parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create parking lot config")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Created(c, apiConfig)
}

func (plc *ParkingLotController) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Deleting parking lot pricing config", map[string]interface{}{
		"method":    "DELETE",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	config, err := plc.adminParkingLotConfigUsecase.GetByID(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to get parking lot config")
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	err = plc.adminParkingLotConfigUsecase.Delete(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to delete parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete parking lot config")
		return
	}

	response.NoContent(c)
}

func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot pricing config", map[string]interface{}{
		"method":    "GET",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	config, err := plc.adminParkingLotConfigUsecase.GetByID(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to get parking lot config")
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

func (plc *ParkingLotController) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Updating parking lot pricing config", map[string]interface{}{
		"method":    "PUT",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}",
		"lot_id":    lotId,
		"config_id": configId,
	})

	var req api.UpdatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	var configName *string
	if req.ConfigName != nil {
		configName = req.ConfigName
	}

	var pricingRules *domain.PricingRules
	if req.PricingRules != nil {
		rules := convertAPIPricingRulesToDomain(*req.PricingRules)
		pricingRules = &rules
	}

	config, err := plc.adminParkingLotConfigUsecase.Update(c.Request.Context(), uuid.UUID(configId), configName, pricingRules)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to update parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to update parking lot config")
		return
	}

	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Activating parking lot pricing config", map[string]interface{}{
		"method":    "POST",
		"path":      "/admin/parking-lots/{lotId}/pricing-configs/{configId}/activate",
		"lot_id":    lotId,
		"config_id": configId,
	})

	var req api.ActivatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	effectiveFrom := req.EffectiveFrom
	var effectiveUntil *time.Time
	if req.EffectiveUntil != nil {
		effectiveUntil = req.EffectiveUntil
	}

	config, err := plc.adminParkingLotConfigUsecase.Activate(c.Request.Context(), uuid.UUID(configId), effectiveFrom, effectiveUntil)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to activate parking lot config")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to activate parking lot config")
		return
	}

	if config.ParkingLotID != uuid.UUID(lotId) {
		response.NotFound(c, "NOT_FOUND", "Parking lot config not found")
		return
	}

	apiConfig := convertParkingLotConfigToAPI(config)
	response.Success(c, apiConfig)
}

func (plc *ParkingLotController) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	plc.logger.LogInfo(c.Request.Context(), "Calculating pricing config preview", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/pricing-configs/calculate-preview",
	})

	var req api.FeeCalculationPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	pricingRules := convertAPIPricingRulesToDomain(req.PricingRules)

	scenarios := make([]usecase.ParkingScenario, len(req.Scenarios))
	for i, scenario := range req.Scenarios {
		scenarios[i] = usecase.ParkingScenario{
			Name:      scenario.Name,
			EntryTime: scenario.EntryTime,
			ExitTime:  scenario.ExitTime,
		}
	}

	results, err := plc.adminParkingLotConfigUsecase.CalculatePreview(c.Request.Context(), pricingRules, scenarios)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to calculate preview")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to calculate preview")
		return
	}

	apiResults := make([]api.ScenarioResult, len(results))
	for i, result := range results {
		apiResults[i] = convertScenarioResultToAPI(result)
	}

	previewResponse := api.FeeCalculationPreviewResponse{
		Scenarios: &apiResults,
	}

	response.Success(c, previewResponse)
}

// Helper functions for converting between API and domain models
func convertParkingLotConfigToAPI(config *domain.ParkingLotConfig) api.ParkingLotConfig {
	configID := types.UUID(config.ID)
	parkingLotID := types.UUID(config.ParkingLotID)
	createdBy := types.UUID(config.CreatedBy)

	pricingRules := convertDomainPricingRulesToAPI(config.PricingRules)
	apiConfig := api.ParkingLotConfig{
		Id:            &configID,
		ParkingLotId:  &parkingLotID,
		ConfigName:    &config.ConfigName,
		IsActive:      &config.IsActive,
		PricingRules:  &pricingRules,
		EffectiveFrom: &config.EffectiveFrom,
		CreatedAt:     &config.CreatedAt,
		UpdatedAt:     &config.UpdatedAt,
		CreatedBy:     &createdBy,
	}

	if config.EffectiveUntil != nil {
		apiConfig.EffectiveUntil = config.EffectiveUntil
	}

	return apiConfig
}

func convertDomainPricingRulesToAPI(rules domain.PricingRules) api.PricingRules {
	apiRules := api.PricingRules{
		LotId:              rules.LotID,
		InitialFreeMinutes: rules.InitialFreeMinutes,
		DailyCap:           rules.DailyCap,
	}

	if len(rules.NightCaps) > 0 {
		nightCaps := make([]api.NightCap, len(rules.NightCaps))
		for i, cap := range rules.NightCaps {
			nightCaps[i] = api.NightCap{
				Start: cap.Start,
				End:   cap.End,
				Cap:   cap.Cap,
			}
		}
		apiRules.NightCaps = &nightCaps
	}

	if len(rules.Overrides) > 0 {
		overrides := make([]api.PriceOverride, len(rules.Overrides))
		for i, override := range rules.Overrides {
			overrides[i] = api.PriceOverride{
				Name:         override.Name,
				Start:        override.Start,
				End:          override.End,
				UnitMinutes:  override.UnitMinutes,
				PricePerUnit: override.PricePerUnit,
			}
		}
		apiRules.Overrides = &overrides
	}

	if len(rules.Rules) > 0 {
		pricingRules := make([]api.PricingRule, len(rules.Rules))
		for i, rule := range rules.Rules {
			days := make([]api.PricingRuleDays, len(rule.Days))
			for j, day := range rule.Days {
				days[j] = api.PricingRuleDays(day)
			}
			pricingRules[i] = api.PricingRule{
				Days:         days,
				Start:        rule.Start,
				End:          rule.End,
				UnitMinutes:  rule.UnitMinutes,
				PricePerUnit: rule.PricePerUnit,
			}
		}
		apiRules.Rules = pricingRules
	}

	return apiRules
}

func convertAPIPricingRulesToDomain(apiRules api.PricingRules) domain.PricingRules {
	rules := domain.PricingRules{
		LotID:              apiRules.LotId,
		InitialFreeMinutes: apiRules.InitialFreeMinutes,
		DailyCap:           apiRules.DailyCap,
	}

	if apiRules.NightCaps != nil {
		nightCaps := make([]domain.NightCap, len(*apiRules.NightCaps))
		for i, cap := range *apiRules.NightCaps {
			nightCaps[i] = domain.NightCap{
				Start: cap.Start,
				End:   cap.End,
				Cap:   cap.Cap,
			}
		}
		rules.NightCaps = nightCaps
	}

	if apiRules.Overrides != nil {
		overrides := make([]domain.PriceOverride, len(*apiRules.Overrides))
		for i, override := range *apiRules.Overrides {
			overrides[i] = domain.PriceOverride{
				Name:         override.Name,
				Start:        override.Start,
				End:          override.End,
				UnitMinutes:  override.UnitMinutes,
				PricePerUnit: override.PricePerUnit,
			}
		}
		rules.Overrides = overrides
	}

	if len(apiRules.Rules) > 0 {
		pricingRules := make([]domain.PricingRule, len(apiRules.Rules))
		for i, rule := range apiRules.Rules {
			days := make([]string, len(rule.Days))
			for j, day := range rule.Days {
				days[j] = string(day)
			}
			pricingRules[i] = domain.PricingRule{
				Days:         days,
				Start:        rule.Start,
				End:          rule.End,
				UnitMinutes:  rule.UnitMinutes,
				PricePerUnit: rule.PricePerUnit,
			}
		}
		rules.Rules = pricingRules
	}

	return rules
}

func convertScenarioResultToAPI(result usecase.ScenarioResult) api.ScenarioResult {
	breakdown := api.FeeBreakdown{
		BaseFee:         &result.Breakdown.BaseFee,
		NightCapApplied: &result.Breakdown.NightCapApplied,
		DailyCapApplied: &result.Breakdown.DailyCapApplied,
		OverrideFee:     &result.Breakdown.OverrideFee,
		DiscountAmount:  &result.Breakdown.DiscountAmount,
		FinalFee:        &result.Breakdown.FinalFee,
	}

	return api.ScenarioResult{
		Name:            &result.Name,
		Fee:             &result.Fee,
		Breakdown:       &breakdown,
		AppliedRules:    &result.AppliedRules,
		FreeMinutesUsed: &result.FreeMinutesUsed,
		TotalMinutes:    &result.TotalMinutes,
		BillableMinutes: &result.BillableMinutes,
	}
}



func (plc *ParkingLotController) PostAdminPricingConfigsValidate(c *gin.Context) {
	plc.logger.LogInfo(c.Request.Context(), "Validating pricing config", map[string]interface{}{
		"method": "POST",
		"path":   "/admin/pricing-configs/validate",
	})

	var req api.PricingRules
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	pricingRules := convertAPIPricingRulesToDomain(req)

	isValid, errors, warnings, err := plc.adminParkingLotConfigUsecase.ValidatePricingRules(c.Request.Context(), pricingRules)
	if err != nil {
		plc.logger.LogError(c.Request.Context(), err, "Failed to validate pricing rules")
		response.InternalServerError(c, "INTERNAL_ERROR", "Failed to validate pricing rules")
		return
	}

	validationResponse := api.ValidationResponse{
		Valid:    &isValid,
		Errors:   &errors,
		Warnings: &warnings,
	}

	response.Success(c, validationResponse)
}
