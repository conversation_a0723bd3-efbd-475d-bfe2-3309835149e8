package usecase

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

// JWTService provides JWT token operations
type JWTService interface {
	GenerateTokenPair(user *domain.User, sessionID uuid.UUID) (*domain.TokenPair, error)
	ValidateAccessToken(tokenString string) (*domain.JWTClaims, error)
	ValidateRefreshToken(tokenString string) (*domain.JWTClaims, error)
}

// EmailService provides basic email sending capabilities
type EmailService interface {
	SendEmail(to, subject, body string) error
	SendTemplatedEmail(template string, to string, data map[string]interface{}) error
}

// EmailTemplateService provides higher-level email functionality using templates
type EmailTemplateService interface {
	EmailService

	// SendEmailVerification sends email verification with token
	SendEmailVerification(user *domain.User, token string) error

	// SendPasswordReset sends password reset email with token
	SendPasswordReset(user *domain.User, token string) error

	// SendWelcomeEmail sends welcome email to new user
	SendWelcomeEmail(user *domain.User) error

	// SendEmailChangeVerification sends verification email for email change
	SendEmailChangeVerification(user *domain.User, newEmail, token string) error

	// SendPaymentMethodAddedEmail sends notification when payment method is added
	SendPaymentMethodAddedEmail(user *domain.User) error

	// SendSetupIntentCanceledEmail sends notification when setup intent is canceled
	SendSetupIntentCanceledEmail(user *domain.User) error

	// SendSetupIntentRequiresActionEmail sends notification when setup intent requires action
	SendSetupIntentRequiresActionEmail(user *domain.User) error

	// SendSetupIntentFailedEmail sends notification when setup intent fails
	SendSetupIntentFailedEmail(user *domain.User, errorMessage string) error

	// SendPaymentFailedEmail sends notification when payment fails
	SendPaymentFailedEmail(user *domain.User, payment *domain.Payment) error
}

type AuthUsecase interface {
	Register(ctx context.Context, req *domain.RegisterRequest) (*domain.User, error)
	Login(ctx context.Context, req *domain.LoginRequest, ipAddress, userAgent string) (*domain.AuthResponse, error)
	RefreshToken(ctx context.Context, req *domain.RefreshTokenRequest, ipAddress, userAgent string) (*domain.TokenPair, error)

	VerifyEmail(ctx context.Context, req *domain.EmailVerificationRequest) (*domain.AuthResponse, error)
	ResendEmailVerification(ctx context.Context, req *domain.ResendVerificationRequest) error

	ForgotPassword(ctx context.Context, req *domain.ForgotPasswordRequest) error
	ResetPassword(ctx context.Context, req *domain.ResetPasswordRequest) error
	ChangePassword(ctx context.Context, userID uuid.UUID, req *domain.ChangePasswordRequest) error

	GetUserSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) (*domain.SessionsResponse, error)
	LogoutSession(ctx context.Context, userID uuid.UUID, sessionID uuid.UUID) error
	LogoutAllOtherSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) error
}

type UserUsecase interface {
	GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error)
	GetByEmail(ctx context.Context, email string) (*domain.User, error)
	UpdateProfile(ctx context.Context, userID uuid.UUID, name, phone *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error)
	UpdateExtendedProfile(ctx context.Context, userID uuid.UUID, name, phone *string, email, username *string, preferredLanguage *domain.LanguageCode, defaultPaymentMethodID *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error)
	List(ctx context.Context, limit, offset int) ([]*domain.User, error)
}

type PlateUsecase interface {
	Create(ctx context.Context, userID uuid.UUID, region, classification, hiragana, serialNumber string, plateType domain.PlateType) (*domain.Plate, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error)
	Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
}

type ParkingLotUsecase interface {
	GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error)
	List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error)
	SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error)
	GetAvailability(ctx context.Context, lotID uuid.UUID) (int, error)
}

type SessionUsecase interface {
	Start(ctx context.Context, parkingLotID uuid.UUID, plateID *uuid.UUID, userID *uuid.UUID) (*domain.Session, error)
	Complete(ctx context.Context, sessionID uuid.UUID, exitTime string) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error)
	GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error)
}

type PaymentUsecase interface {
	CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error)
	ProcessWebhook(ctx context.Context, eventType string, data map[string]interface{}) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error)
	ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error)
}

type WebhookUsecase interface {
	ProcessStripeWebhook(ctx context.Context, payload []byte, signature string) error
}

type AutoPaymentProcessor interface {
	ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error)
}

type PaymentMethodUsecase interface {
	CreateSetupIntent(ctx context.Context, userID uuid.UUID) (*domain.SetupIntent, error)
	AttachPaymentMethod(ctx context.Context, userID uuid.UUID, stripePaymentMethodID, setupIntentID string, isDefault bool) (*domain.PaymentMethod, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.PaymentMethod, error)
	GetByID(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*domain.PaymentMethod, error)
	SetAsDefault(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ValidateSetupIntent(ctx context.Context, setupIntentID string) (*domain.SetupIntent, error)
	ProcessWebhook(ctx context.Context, eventType string, data map[string]interface{}) error
}

type BookingUsecase interface {
	Create(ctx context.Context, userID, plateID, parkingLotID uuid.UUID, startTime, endTime string) (*domain.Booking, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error)
	Cancel(ctx context.Context, bookingID uuid.UUID, userID uuid.UUID) error
}

type NotificationUsecase interface {
	Create(ctx context.Context, userID uuid.UUID, notificationType domain.NotificationType, title, message string) (*domain.Notification, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error)
	MarkAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error
	SendPendingNotifications(ctx context.Context) error
}

type HardwareUsecase interface {
	ProcessDetection(ctx context.Context, plateNumber string, parkingLotID uuid.UUID, confidence float64, imageURL *string) error
}

// Admin interfaces for administrative operations
type AdminParkingLotUsecase interface {
	Create(ctx context.Context, name, address string, latitude, longitude float64, totalSpots, hourlyRate int, features []string) (*domain.ParkingLot, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error)
	List(ctx context.Context, status *domain.LotStatus, search *string, limit, offset int) ([]*domain.ParkingLot, int, error)
	Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.ParkingLot, error)
	Delete(ctx context.Context, id uuid.UUID) error
	GetWithStats(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, map[string]interface{}, error)
}

type AdminUserUsecase interface {
	List(ctx context.Context, status *domain.UserStatus, role *domain.UserRole, search *string, emailVerified *bool, limit, offset int) ([]*domain.User, int, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error)
	Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.User, error)
	Suspend(ctx context.Context, id uuid.UUID, reason string) error
	Activate(ctx context.Context, id uuid.UUID) error
}

type AdminPlateUsecase interface {
	List(ctx context.Context, userID *uuid.UUID, plateNumber *string, plateType *domain.PlateType, isActive *bool, limit, offset int) ([]*domain.Plate, int, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error)
	Delete(ctx context.Context, id uuid.UUID) error
	Activate(ctx context.Context, id uuid.UUID) error
	Deactivate(ctx context.Context, id uuid.UUID) error
}

type AdminSessionUsecase interface {
	List(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Session, int, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error)
	Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.Session, error)
	Complete(ctx context.Context, id uuid.UUID, exitTime *string, amount *int, reason string) (*domain.Session, error)
	Cancel(ctx context.Context, id uuid.UUID, reason string) (*domain.Session, error)
}

type AdminPaymentUsecase interface {
	List(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Payment, int, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Payment, error)
	ProcessRefund(ctx context.Context, id uuid.UUID, amount *int, reason string) (*domain.Payment, error)
}

type AdminAnalyticsUsecase interface {
	GetDashboardAnalytics(ctx context.Context, period string) (map[string]interface{}, error)
	GetRevenueAnalytics(ctx context.Context, period string, dateFrom, dateTo *string) (map[string]interface{}, error)
	GetUsageAnalytics(ctx context.Context, period string, dateFrom, dateTo *string) (map[string]interface{}, error)
	ExportReport(ctx context.Context, reportType, format string, filters map[string]interface{}) (map[string]interface{}, error)
}

type AdminParkingLotConfigUsecase interface {
	GetByParkingLotID(ctx context.Context, lotID uuid.UUID, includeInactive bool) ([]*domain.ParkingLotConfig, error)
	Create(ctx context.Context, lotID uuid.UUID, configName string, pricingRules domain.PricingRules, createdBy uuid.UUID) (*domain.ParkingLotConfig, error)
	GetByID(ctx context.Context, configID uuid.UUID) (*domain.ParkingLotConfig, error)
	Update(ctx context.Context, configID uuid.UUID, configName *string, pricingRules *domain.PricingRules) (*domain.ParkingLotConfig, error)
	Delete(ctx context.Context, configID uuid.UUID) error
	Activate(ctx context.Context, configID uuid.UUID, effectiveFrom time.Time, effectiveUntil *time.Time) (*domain.ParkingLotConfig, error)
	ValidatePricingRules(ctx context.Context, pricingRules domain.PricingRules) (bool, []string, []string, error)
	CalculatePreview(ctx context.Context, pricingRules domain.PricingRules, scenarios []ParkingScenario) ([]ScenarioResult, error)
}

type ParkingScenario struct {
	Name      string
	EntryTime time.Time
	ExitTime  time.Time
}

type ScenarioResult struct {
	Name            string
	Fee             int
	Breakdown       FeeBreakdown
	AppliedRules    []string
	FreeMinutesUsed int
	TotalMinutes    int
	BillableMinutes int
}

type FeeBreakdown struct {
	BaseFee          int
	NightCapApplied  int
	DailyCapApplied  int
	OverrideFee      int
	DiscountAmount   int
	FinalFee         int
}

type UseCases struct {
	Auth          AuthUsecase
	User          UserUsecase
	Plate         PlateUsecase
	ParkingLot    ParkingLotUsecase
	Session       SessionUsecase
	Payment       PaymentUsecase
	PaymentMethod PaymentMethodUsecase
	Booking       BookingUsecase
	Notification  NotificationUsecase
	Hardware      HardwareUsecase
	// Admin usecases
	AdminParkingLot       AdminParkingLotUsecase
	AdminParkingLotConfig AdminParkingLotConfigUsecase
	AdminUser             AdminUserUsecase
	AdminPlate            AdminPlateUsecase
	AdminSession          AdminSessionUsecase
	AdminPayment          AdminPaymentUsecase
	AdminAnalytics        AdminAnalyticsUsecase
}
