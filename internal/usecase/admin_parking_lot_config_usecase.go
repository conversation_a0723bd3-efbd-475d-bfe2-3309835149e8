package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/internal/utils/parking_fee"
)

type adminParkingLotConfigUsecase struct {
	configRepo     repository.ParkingLotConfigRepository
	parkingLotRepo repository.ParkingLotRepository
	feeCalculator  *parking_fee.Calculator
}

func NewAdminParkingLotConfigUsecase(
	configRepo repository.ParkingLotConfigRepository,
	parkingLotRepo repository.ParkingLotRepository,
) AdminParkingLotConfigUsecase {
	return &adminParkingLotConfigUsecase{
		configRepo:     configRepo,
		parkingLotRepo: parkingLotRepo,
		feeCalculator:  parking_fee.NewCalculator(),
	}
}

func (uc *adminParkingLotConfigUsecase) GetByParkingLotID(ctx context.Context, lotID uuid.UUID, includeInactive bool) ([]*domain.ParkingLotConfig, error) {
	lot, err := uc.parkingLotRepo.GetByID(ctx, lotID)
	if err != nil {
		return nil, fmt.Errorf("parking lot not found: %w", err)
	}
	if lot == nil {
		return nil, fmt.Errorf("parking lot not found")
	}

	configs, err := uc.configRepo.GetByParkingLotID(ctx, lotID, includeInactive)
	if err != nil {
		return nil, fmt.Errorf("failed to get parking lot configs: %w", err)
	}

	return configs, nil
}

func (uc *adminParkingLotConfigUsecase) Create(ctx context.Context, lotID uuid.UUID, configName string, pricingRules domain.PricingRules, createdBy uuid.UUID) (*domain.ParkingLotConfig, error) {
	lot, err := uc.parkingLotRepo.GetByID(ctx, lotID)
	if err != nil {
		return nil, fmt.Errorf("parking lot not found: %w", err)
	}
	if lot == nil {
		return nil, fmt.Errorf("parking lot not found")
	}

	if configName == "" {
		return nil, fmt.Errorf("config name is required")
	}

	if createdBy == uuid.Nil {
		return nil, fmt.Errorf("created by user ID is required")
	}

	pricingRules.LotID = lotID.String()

	config, err := domain.NewParkingLotConfig(lotID, configName, pricingRules, createdBy)
	if err != nil {
		return nil, fmt.Errorf("failed to create parking lot config: %w", err)
	}

	if err := uc.configRepo.Create(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to save parking lot config: %w", err)
	}

	return config, nil
}

func (uc *adminParkingLotConfigUsecase) GetByID(ctx context.Context, configID uuid.UUID) (*domain.ParkingLotConfig, error) {
	config, err := uc.configRepo.GetByID(ctx, configID)
	if err != nil {
		return nil, fmt.Errorf("parking lot config not found: %w", err)
	}
	if config == nil {
		return nil, fmt.Errorf("parking lot config not found")
	}

	return config, nil
}

func (uc *adminParkingLotConfigUsecase) Update(ctx context.Context, configID uuid.UUID, configName *string, pricingRules *domain.PricingRules) (*domain.ParkingLotConfig, error) {
	config, err := uc.configRepo.GetByID(ctx, configID)
	if err != nil {
		return nil, fmt.Errorf("parking lot config not found: %w", err)
	}
	if config == nil {
		return nil, fmt.Errorf("parking lot config not found")
	}

	if config.IsActive {
		return nil, fmt.Errorf("cannot update active configuration")
	}

	if configName != nil && *configName != "" {
		config.ConfigName = *configName
	}

	if pricingRules != nil {
		pricingRules.LotID = config.ParkingLotID.String()
		if err := config.UpdatePricingRules(*pricingRules); err != nil {
			return nil, fmt.Errorf("failed to update pricing rules: %w", err)
		}
	}

	if err := uc.configRepo.Update(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to update parking lot config: %w", err)
	}

	return config, nil
}

func (uc *adminParkingLotConfigUsecase) Delete(ctx context.Context, configID uuid.UUID) error {
	config, err := uc.configRepo.GetByID(ctx, configID)
	if err != nil {
		return fmt.Errorf("parking lot config not found: %w", err)
	}
	if config == nil {
		return fmt.Errorf("parking lot config not found")
	}

	if config.IsActive {
		return fmt.Errorf("cannot delete active configuration")
	}

	if err := uc.configRepo.Delete(ctx, configID); err != nil {
		return fmt.Errorf("failed to delete parking lot config: %w", err)
	}

	return nil
}

func (uc *adminParkingLotConfigUsecase) Activate(ctx context.Context, configID uuid.UUID, effectiveFrom time.Time, effectiveUntil *time.Time) (*domain.ParkingLotConfig, error) {
	config, err := uc.configRepo.GetByID(ctx, configID)
	if err != nil {
		return nil, fmt.Errorf("parking lot config not found: %w", err)
	}
	if config == nil {
		return nil, fmt.Errorf("parking lot config not found")
	}

	if err := uc.configRepo.DeactivateAllForParkingLot(ctx, config.ParkingLotID); err != nil {
		return nil, fmt.Errorf("failed to deactivate existing configs: %w", err)
	}

	if err := config.Activate(effectiveFrom, effectiveUntil); err != nil {
		return nil, fmt.Errorf("failed to activate config: %w", err)
	}

	if err := uc.configRepo.Update(ctx, config); err != nil {
		return nil, fmt.Errorf("failed to save activated config: %w", err)
	}

	return config, nil
}

func (uc *adminParkingLotConfigUsecase) ValidatePricingRules(ctx context.Context, pricingRules domain.PricingRules) (bool, []string, []string, error) {
	var errors []string
	var warnings []string

	if pricingRules.InitialFreeMinutes < 0 {
		errors = append(errors, "initial free minutes cannot be negative")
	}

	if pricingRules.DailyCap < 0 {
		errors = append(errors, "daily cap cannot be negative")
	}

	if len(pricingRules.Rules) == 0 {
		errors = append(errors, "at least one pricing rule is required")
	}

	for i, rule := range pricingRules.Rules {
		if len(rule.Days) == 0 {
			errors = append(errors, fmt.Sprintf("rule %d: days cannot be empty", i+1))
		}
		if rule.UnitMinutes <= 0 {
			errors = append(errors, fmt.Sprintf("rule %d: unit minutes must be positive", i+1))
		}
		if rule.PricePerUnit < 0 {
			errors = append(errors, fmt.Sprintf("rule %d: price per unit cannot be negative", i+1))
		}
		if !uc.isValidTimeFormat(rule.Start) {
			errors = append(errors, fmt.Sprintf("rule %d: invalid start time format", i+1))
		}
		if !uc.isValidTimeFormat(rule.End) && rule.End != "24:00" {
			errors = append(errors, fmt.Sprintf("rule %d: invalid end time format", i+1))
		}
	}

	for i, cap := range pricingRules.NightCaps {
		if cap.Cap < 0 {
			errors = append(errors, fmt.Sprintf("night cap %d: cap cannot be negative", i+1))
		}
		if !uc.isValidTimeFormat(cap.Start) {
			errors = append(errors, fmt.Sprintf("night cap %d: invalid start time format", i+1))
		}
		if !uc.isValidTimeFormat(cap.End) {
			errors = append(errors, fmt.Sprintf("night cap %d: invalid end time format", i+1))
		}
	}

	for i, override := range pricingRules.Overrides {
		if override.UnitMinutes <= 0 {
			errors = append(errors, fmt.Sprintf("override %d: unit minutes must be positive", i+1))
		}
		if override.PricePerUnit < 0 {
			errors = append(errors, fmt.Sprintf("override %d: price per unit cannot be negative", i+1))
		}
		if _, err := time.Parse("2006-01-02T15:04", override.Start); err != nil {
			errors = append(errors, fmt.Sprintf("override %d: invalid start datetime format", i+1))
		}
		if _, err := time.Parse("2006-01-02T15:04", override.End); err != nil {
			errors = append(errors, fmt.Sprintf("override %d: invalid end datetime format", i+1))
		}
	}

	if pricingRules.InitialFreeMinutes > 120 {
		warnings = append(warnings, "initial free minutes is unusually high (>2 hours)")
	}

	if pricingRules.DailyCap > 10000 {
		warnings = append(warnings, "daily cap is unusually high (>¥10,000)")
	}

	isValid := len(errors) == 0
	return isValid, errors, warnings, nil
}

func (uc *adminParkingLotConfigUsecase) CalculatePreview(ctx context.Context, pricingRules domain.PricingRules, scenarios []ParkingScenario) ([]ScenarioResult, error) {
	results := make([]ScenarioResult, len(scenarios))

	for i, scenario := range scenarios {
		if scenario.ExitTime.Before(scenario.EntryTime) || scenario.ExitTime.Equal(scenario.EntryTime) {
			return nil, fmt.Errorf("scenario %d: exit time must be after entry time", i+1)
		}

		fee, err := uc.feeCalculator.CalculateParkingFee(scenario.EntryTime, scenario.ExitTime, pricingRules)
		if err != nil {
			return nil, fmt.Errorf("scenario %d: failed to calculate fee: %w", i+1, err)
		}

		totalMinutes := int(scenario.ExitTime.Sub(scenario.EntryTime).Minutes())
		freeMinutesUsed := pricingRules.InitialFreeMinutes
		if totalMinutes < freeMinutesUsed {
			freeMinutesUsed = totalMinutes
		}
		billableMinutes := totalMinutes - freeMinutesUsed

		appliedRules := uc.getAppliedRules(pricingRules, scenario.EntryTime, scenario.ExitTime)

		breakdown := FeeBreakdown{
			BaseFee:         fee,
			NightCapApplied: 0,
			DailyCapApplied: 0,
			OverrideFee:     0,
			DiscountAmount:  0,
			FinalFee:        fee,
		}

		if pricingRules.DailyCap > 0 && fee > pricingRules.DailyCap {
			breakdown.DailyCapApplied = fee - pricingRules.DailyCap
			breakdown.FinalFee = pricingRules.DailyCap
		}

		results[i] = ScenarioResult{
			Name:            scenario.Name,
			Fee:             fee,
			Breakdown:       breakdown,
			AppliedRules:    appliedRules,
			FreeMinutesUsed: freeMinutesUsed,
			TotalMinutes:    totalMinutes,
			BillableMinutes: billableMinutes,
		}
	}

	return results, nil
}

func (uc *adminParkingLotConfigUsecase) getAppliedRules(pricingRules domain.PricingRules, entryTime, exitTime time.Time) []string {
	var appliedRules []string

	for _, override := range pricingRules.Overrides {
		start, err := time.Parse("2006-01-02T15:04", override.Start)
		if err != nil {
			continue
		}
		end, err := time.Parse("2006-01-02T15:04", override.End)
		if err != nil {
			continue
		}

		if (entryTime.Before(end) || entryTime.Equal(end)) && (exitTime.After(start) || exitTime.Equal(start)) {
			appliedRules = append(appliedRules, fmt.Sprintf("Override: %s", override.Name))
		}
	}

	weekdays := []string{"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"}
	current := entryTime
	for current.Before(exitTime) {
		weekday := weekdays[int(current.Weekday()+6)%7]
		for _, rule := range pricingRules.Rules {
			for _, day := range rule.Days {
				if day == weekday {
					appliedRules = append(appliedRules, fmt.Sprintf("Rule: %s %s-%s", weekday, rule.Start, rule.End))
					break
				}
			}
		}
		current = current.Add(24 * time.Hour)
	}

	for _, cap := range pricingRules.NightCaps {
		appliedRules = append(appliedRules, fmt.Sprintf("Night Cap: %s-%s (¥%d)", cap.Start, cap.End, cap.Cap))
	}

	if pricingRules.DailyCap > 0 {
		appliedRules = append(appliedRules, fmt.Sprintf("Daily Cap: ¥%d", pricingRules.DailyCap))
	}

	return appliedRules
}

func (uc *adminParkingLotConfigUsecase) isValidTimeFormat(timeStr string) bool {
	if timeStr == "24:00" {
		return true
	}
	_, err := time.Parse("15:04", timeStr)
	return err == nil
}
